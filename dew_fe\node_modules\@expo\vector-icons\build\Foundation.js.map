{"version": 3, "file": "Foundation.js", "sourceRoot": "", "sources": ["../src/Foundation.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,IAAI,MAAM,yDAAyD,CAAC;AAC3E,OAAO,QAAQ,MAAM,8DAA8D,CAAC;AAEpF,eAAe,aAAa,CAAC,QAAQ,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC", "sourcesContent": ["\"use client\";\n\nimport createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/Foundation.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/Foundation.json';\n\nexport default createIconSet(glyphMap, 'foundation', font);\n"]}