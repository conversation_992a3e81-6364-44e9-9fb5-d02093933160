{"name": "@expo/cli", "version": "0.24.20", "description": "The Expo CLI", "main": "build/bin/cli", "bin": {"expo-internal": "build/bin/cli"}, "files": ["static", "build"], "scripts": {"build": "taskr", "prepare": "taskr release", "clean": "expo-module clean", "lint": "expo-module lint", "typecheck": "expo-module typecheck", "test": "expo-module test", "test:e2e": "jest --config e2e/jest.config.js", "test:playwright": "playwright test --config e2e/playwright.config.ts", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module", "generate-graphql-code": "graphql-codegen --config graphql-codegen.yml"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/cli"}, "keywords": ["expo", "cli"], "author": "Expo", "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/cli", "dependencies": {"@0no-co/graphql.web": "^1.0.8", "@babel/runtime": "^7.20.0", "@expo/code-signing-certificates": "^0.0.5", "@expo/config": "~11.0.13", "@expo/config-plugins": "~10.1.2", "@expo/devcert": "^1.1.2", "@expo/env": "~1.0.7", "@expo/image-utils": "^0.7.6", "@expo/json-file": "^9.1.5", "@expo/metro-config": "~0.20.17", "@expo/osascript": "^2.2.5", "@expo/package-manager": "^1.8.6", "@expo/plist": "^0.3.5", "@expo/prebuild-config": "^9.0.11", "@expo/spawn-async": "^1.7.2", "@expo/ws-tunnel": "^1.0.1", "@expo/xcpretty": "^4.3.0", "@react-native/dev-middleware": "0.79.5", "@urql/core": "^5.0.6", "@urql/exchange-retry": "^1.3.0", "accepts": "^1.3.8", "arg": "^5.0.2", "better-opn": "~3.0.2", "bplist-creator": "0.1.0", "bplist-parser": "^0.3.1", "chalk": "^4.0.0", "ci-info": "^3.3.0", "compression": "^1.7.4", "connect": "^3.7.0", "debug": "^4.3.4", "env-editor": "^0.4.1", "freeport-async": "^2.0.0", "getenv": "^2.0.0", "glob": "^10.4.2", "minimatch": "^9.0.0", "lan-network": "^0.1.6", "node-forge": "^1.3.1", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "picomatch": "^3.0.1", "pretty-bytes": "^5.6.0", "pretty-format": "^29.7.0", "progress": "^2.0.3", "prompts": "^2.3.2", "qrcode-terminal": "0.11.0", "require-from-string": "^2.0.2", "requireg": "^0.2.2", "resolve": "^1.22.2", "resolve-from": "^5.0.0", "resolve.exports": "^2.0.3", "semver": "^7.6.0", "send": "^0.19.0", "slugify": "^1.3.4", "source-map-support": "~0.5.21", "stacktrace-parser": "^0.1.10", "structured-headers": "^0.4.1", "tar": "^7.4.3", "terminal-link": "^2.1.1", "undici": "^6.18.2", "wrap-ansi": "^7.0.0", "ws": "^8.12.1"}, "taskr": {"requires": ["./taskfile-swc.js"]}, "devDependencies": {"@expo/multipart-body-parser": "^1.0.0", "@expo/ngrok": "4.1.3", "@expo/server": "^0.6.3", "@graphql-codegen/cli": "^2.16.3", "@graphql-codegen/typescript": "^2.8.7", "@graphql-codegen/typescript-operations": "^2.5.12", "@playwright/test": "^1.53.1", "@swc/core": "^1.11.11", "@taskr/clear": "^1.1.0", "@taskr/esnext": "^1.1.0", "@taskr/watch": "^1.1.0", "@types/accepts": "^1.3.5", "@types/connect": "^3.4.33", "@types/cross-spawn": "^6.0.6", "@types/debug": "^4.1.7", "@types/execa": "^0.9.0", "@types/getenv": "^1.0.0", "@types/klaw-sync": "^6.0.0", "@types/node": "^22.14.0", "@types/npm-package-arg": "^6.1.0", "@types/picomatch": "^2.3.3", "@types/progress": "^2.0.5", "@types/prompts": "^2.0.6", "@types/resolve": "^1.20.2", "@types/semver": "^7.5.8", "@types/send": "^0.17.1", "@types/webpack": "~4.41.32", "@types/webpack-dev-server": "^3.11.0", "@types/wrap-ansi": "^8.0.1", "@types/ws": "^8.5.4", "devtools-protocol": "^0.0.1113120", "expo-atlas": "^0.4.1", "expo-module-scripts": "^4.1.9", "find-process": "^1.4.7", "jest-runner-tsd": "^6.0.0", "klaw-sync": "^6.0.0", "memfs": "^3.2.0", "nock": "14.0.0-beta.7", "node-html-parser": "^6.1.5", "nullthrows": "^1.1.1", "playwright": "^1.53.1", "taskr": "^1.1.0", "tree-kill": "^1.2.2", "tsd": "^0.28.1"}, "gitHead": "134c147ee4274f9688929ac66cfef950947659d0"}