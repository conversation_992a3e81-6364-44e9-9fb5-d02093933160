{"version": 3, "file": "createIconSetFromFontAwesome6.js", "sourceRoot": "", "sources": ["../src/createIconSetFromFontAwesome6.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,OAAO,uBAAuB,MAAM,2BAA2B,CAAC;AAEhE,MAAM,QAAQ,GAAG;IACf,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,UAAU,EAAE,YAAY;IACxB,UAAU,EAAE,YAAY;IACxB,OAAO,EAAE,SAAS;IAClB,IAAI,EAAE,MAAM;CACb,CAAC;AAEF,SAAS,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,GAAG,KAAK;IACnE,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3C,MAAM,UAAU,GAAG,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IAEzD,SAAS,cAAc,CAAC,KAAK;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAChD,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC3C,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YAChD,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,SAAS,cAAc,CAAC,KAAK,EAAE,KAAK;QAClC,IAAI,MAAM,GAAG,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;QAClD,MAAM,GAAG,KAAK,KAAK,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC;QACzD,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QACtD,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,SAAS,sBAAsB,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,GAAG,UAAU;QACpE,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;QAElC,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;YAC3B,SAAS,GAAG,SAAS,CAAC;QACxB,CAAC;QAED,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,SAAS,GAAG,OAAO,CAAC;QACtB,CAAC;QAED,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAE5C,OAAO;YACL,UAAU,EAAE,GAAG,MAAM,IAAI,SAAS,EAAE;YACpC,QAAQ;YACR,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC;gBACzB,GAAG,EAAE;oBACH,UAAU;iBACX;gBACD,OAAO,EAAE,EAAE;aACZ,CAAC;YACF,QAAQ;SACT,CAAC;IACJ,CAAC;IAED,MAAM,UAAU,GAAG,sBAAsB,CAAC,QAAQ,EAAE,KAAK,EAAE,oBAAoB,CAAC,CAAC;IACjF,MAAM,UAAU,GAAG,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC1D,MAAM,YAAY,GAAG,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC9D,MAAM,UAAU,GAAG,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC1D,MAAM,eAAe,GAAG,sBAAsB,CAAC,aAAa,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC;IAC1F,MAAM,UAAU,GAAG,sBAAsB,CAAC,eAAe,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC;IACvF,MAAM,eAAe,GAAG,sBAAsB,CAAC,aAAa,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC;IAC1F,MAAM,YAAY,GAAG,sBAAsB,CAAC,SAAS,EAAE,KAAK,EAAE,qBAAqB,CAAC,CAAC;IACrF,MAAM,SAAS,GAAG,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACxD,MAAM,IAAI,GAAG,uBAAuB,CAClC;QACE,KAAK,EAAE,UAAU;QACjB,KAAK,EAAE,UAAU;QACjB,OAAO,EAAE,YAAY;QACrB,KAAK,EAAE,UAAU;QACjB,KAAK,EAAE,UAAU;QACjB,UAAU,EAAE,eAAe;QAC3B,UAAU,EAAE,eAAe;QAC3B,OAAO,EAAE,YAAY;QACrB,IAAI,EAAE,SAAS;KAChB,EACD;QACE,YAAY,EAAE,SAAS;QACvB,cAAc;QACd,cAAc;KACf,CACF,CAAC;IAEF,OAAO,IAAI,CAAC;AACd,CAAC;AAED,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,CAAC", "sourcesContent": ["import { Platform } from 'react-native';\n\nimport createMultiStyleIconSet from './createMultiStyleIconSet';\n\nconst FA6Style = {\n  regular: 'regular',\n  light: 'light',\n  solid: 'solid',\n  brand: 'brand',\n  sharp: 'sharp',\n  sharpLight: 'sharpLight',\n  sharpSolid: 'sharpSolid',\n  duotone: 'duotone',\n  thin: 'thin',\n};\n\nfunction createFA6iconSet(glyphMap, metadata = {}, fonts, pro = false) {\n  const metadataKeys = Object.keys(metadata);\n  const fontFamily = `FontAwesome6${pro ? 'Pro' : 'Free'}`;\n\n  function fallbackFamily(glyph) {\n    for (let i = 0; i < metadataKeys.length; i += 1) {\n      const family = metadataKeys[i];\n      if (metadata[family].indexOf(glyph) !== -1) {\n        return family === 'brands' ? 'brand' : family;\n      }\n    }\n\n    return 'regular';\n  }\n\n  function glyphValidator(glyph, style) {\n    let family = style === 'brand' ? 'brands' : style;\n    family = style === 'sharpSolid' ? 'sharp-solid' : family;\n    if (metadataKeys.indexOf(family) === -1) return false;\n    return metadata[family].indexOf(glyph) !== -1;\n  }\n\n  function createFontAwesomeStyle(style, fontWeight, family = fontFamily) {\n    let styleName = style;\n\n    const fontFile = fonts[styleName];\n\n    if (styleName === 'Brands') {\n      styleName = 'Regular';\n    }\n\n    if (styleName === 'Duotone') {\n      styleName = 'Solid';\n    }\n\n    styleName = styleName.replace('Sharp_', '');\n\n    return {\n      fontFamily: `${family}-${styleName}`,\n      fontFile,\n      fontStyle: Platform.select({\n        ios: {\n          fontWeight,\n        },\n        default: {},\n      }),\n      glyphMap,\n    };\n  }\n\n  const brandIcons = createFontAwesomeStyle('Brands', '400', 'FontAwesome6Brands');\n  const lightIcons = createFontAwesomeStyle('Light', '300');\n  const regularIcons = createFontAwesomeStyle('Regular', '400');\n  const solidIcons = createFontAwesomeStyle('Solid', '900');\n  const sharpLightIcons = createFontAwesomeStyle('Sharp_Light', '300', 'FontAwesome6Sharp');\n  const sharpIcons = createFontAwesomeStyle('Sharp_Regular', '400', 'FontAwesome6Sharp');\n  const sharpSolidIcons = createFontAwesomeStyle('Sharp_Solid', '900', 'FontAwesome6Sharp');\n  const duotoneIcons = createFontAwesomeStyle('Duotone', '900', 'FontAwesome6Duotone');\n  const thinIcons = createFontAwesomeStyle('Thin', '100');\n  const Icon = createMultiStyleIconSet(\n    {\n      brand: brandIcons,\n      light: lightIcons,\n      regular: regularIcons,\n      solid: solidIcons,\n      sharp: sharpIcons,\n      sharpLight: sharpLightIcons,\n      sharpSolid: sharpSolidIcons,\n      duotone: duotoneIcons,\n      thin: thinIcons,\n    },\n    {\n      defaultStyle: 'regular',\n      fallbackFamily,\n      glyphValidator,\n    }\n  );\n\n  return Icon;\n}\n\nexport { createFA6iconSet, FA6Style };\n"]}