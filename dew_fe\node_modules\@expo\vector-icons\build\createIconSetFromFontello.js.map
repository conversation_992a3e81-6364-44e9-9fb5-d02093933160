{"version": 3, "file": "createIconSetFromFontello.js", "sourceRoot": "", "sources": ["../src/createIconSetFromFontello.ts"], "names": [], "mappings": "AAAA,OAAO,yBAAyB,MAAM,sEAAsE,CAAC;AAE7G,MAAM,CAAC,OAAO,WAAW,MAAM,EAAE,YAAY,EAAE,WAAW;IACxD,OAAO,yBAAyB,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;AACtE,CAAC", "sourcesContent": ["import createIconSet<PERSON><PERSON><PERSON><PERSON>llo from './vendor/react-native-vector-icons/lib/create-icon-set-from-fontello';\n\nexport default function (config, expoFontName, expoAssetId) {\n  return createIconSetFromFontello(config, expoFontName, expoAssetId);\n}\n"]}