{"buildFiles": ["D:\\side_project\\dew_fe\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\side_project\\dew_fe\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\181jm5p4\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\side_project\\dew_fe\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\181jm5p4\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"gesturehandler::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "gesturehandler", "output": "D:\\side_project\\dew_fe\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\181jm5p4\\obj\\x86_64\\libgesturehandler.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}