{"version": 3, "file": "Ionicons.js", "sourceRoot": "", "sources": ["../src/Ionicons.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,IAAI,MAAM,uDAAuD,CAAC;AACzE,OAAO,QAAQ,MAAM,4DAA4D,CAAC;AAElF,eAAe,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC", "sourcesContent": ["\"use client\";\n\nimport createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/Ionicons.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/Ionicons.json';\n\nexport default createIconSet(glyphMap, 'ionicons', font);\n"]}